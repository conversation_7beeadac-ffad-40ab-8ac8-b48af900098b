import Image from "next/image";
import React from "react";
import Icon from "@/components/ui/icons/icon";
import InlineLink from "@/components/ui/inline_link/inline_link";
import { IconName } from "@/components/ui/icons/icon_config";
import { ProductLink, ProductsProps } from "./product_config";

type ProductProps = {
  product: ProductsProps;
};

export default function Product({ product }: ProductProps) {
  return (
    <div className="py-8 grid grid-cols-1 md:grid-cols-2 gap-10">
      <div>
        <h3 className="text-xl md:text-2xl font-semibold font-montserrat pb-3">
          {product.name}
        </h3>
        <p className="text-secondary-foreground pb-3 lg:text-lg ">
          {product.description}
        </p>
        <ul className="flex flex-col gap-4 pt-3">
          {product.links?.map((link, index) => (
            <li key={index}>
              <InlineLink
                icon={link.icon}
                href={link.href}
                iconSize={24}
                iconColor={product.icon_color}
              >
                {link.name}
              </InlineLink>
            </li>
          ))}
        </ul>
      </div>
      <div>
        <Image
          src={product.image}
          alt={product.name}
          width={500}
          height={500}
          className="w-full h-auto"
        />
      </div>
    </div>
  );
}
